# Cấu trúc Config Mới

## Tổng quan

Config đã được tách thành 2 loại để quản lý tốt hơn:

### 1. Game Config (`game_settings.json`)
**Chung cho tất cả account** - Những cài đặt không thay đổi theo account:

- **ADB Settings**: Đường dẫn ADB, LDConsole
- **Game Info**: Package name, Activity name
- **System Settings**: Loop delay, Log level, Save log
- **UI Coordinates**: Tọa độ username, password, minimap, xa phu
- **Train Areas**: Danh sách khu vực và map train có sẵn

### 2. Default Account Config (`default_account_settings.json`)
**Mặc định cho account mới** - Những cài đặt có thể khác nhau cho từng account:

- **Auto Settings**: Auto login, Login retry, Auto train
- **Train Settings**: Train area, Train map, Train position (x, y)

## Cách sử dụng

### Trong Settings Dialog

1. **Tab "Cài đặt Game"**: Chỉnh sửa config chung cho game
2. **Tab "Mặc định Account"**: Chỉnh sửa config mặc định cho account mới

### Khi tạo Account mới

- Account mới sẽ tự động sử dụng giá trị từ Default Account Config
- Có thể override trong Account Dialog nếu cần

### Trong Code

```python
from src.config.config import (
    load_game_config,
    load_default_account_config,
    save_game_config,
    save_default_account_config,
    get_merged_config
)

# Load riêng biệt
game_config = load_game_config()
account_config = load_default_account_config()

# Load merged (để tương thích)
merged_config = get_merged_config()
```

## Files được tạo

1. `game_settings.json` - Config game chung
2. `default_account_settings.json` - Config mặc định cho account

## Lợi ích

1. **Tách biệt rõ ràng**: Game settings vs Account settings
2. **Dễ quản lý**: Mỗi loại config có file riêng
3. **Giao diện sạch sẽ**: Chỉ 2 tab chính, không có settings cũ
4. **Linh hoạt**: Account có thể có settings riêng
5. **Bảo trì dễ dàng**: Thay đổi game settings không ảnh hưởng account settings

## Thay đổi

- **Đã bỏ**: Tất cả tab settings cũ
- **Chỉ giữ**: 2 tab mới với cấu trúc rõ ràng
- **Tương thích**: Code backend vẫn hỗ trợ load config cũ nếu cần
