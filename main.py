import sys
import logging
from PyQt5.QtWidgets import QApplication
from src.gui.main_window import MainWindow
from src.config.config import load_config, load_game_config

def setup_logging():
    # Use game config for logging settings
    config = load_game_config()
    # Fallback to legacy config if game config doesn't exist
    if not config:
        config = load_config()
    log_level = getattr(logging, config.get('log_level', 'INFO'))
    
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler()
        ]
    )
    
    if config.get('save_log', True):
        file_handler = logging.FileHandler('auto_vltk.log', encoding='utf-8')
        file_handler.setLevel(log_level)
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        logging.getLogger().addHandler(file_handler)


def main():
    setup_logging()
    
    app = QApplication(sys.argv)
    app.setApplicationName("Auto VLTK")
    app.setOrganizationName("VLTKAuto")
    
    window = MainWindow()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()