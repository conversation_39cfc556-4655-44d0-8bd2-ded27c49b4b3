import threading
import time
import logging
from typing import Dict, List, Optional, Callable
from dataclasses import dataclass, field
from enum import Enum
import json
import os
import queue

logger = logging.getLogger(__name__)


class AccountStatus(Enum):
    IDLE = "idle"
    PENDING = "pending"  # Waiting in queue to be processed
    RUNNING = "running"  # Currently being processed by a worker
    PAUSED = "paused"
    ERROR = "error"
    STOPPED = "stopped"


# Party sync status constants
class PartySyncStatus:
    SKIPPED_NO_PARTY = "skipped_no_party"
    SKIPPED_OTHER_PARTY_WAITING = "skipped_other_party_waiting"
    SUCCESS = "success"
    TIMEOUT = "timeout"


@dataclass
class Account:
    id: str
    name: str
    device_id: str
    username: str
    password: str
    party: str = ""  # Party name for synchronization
    server: str = ""
    character: str = ""
    loop: bool = False  # Enable continuous loop for this account
    status: AccountStatus = AccountStatus.IDLE
    last_action_time: float = 0
    error_message: str = ""
    settings: Dict = field(default_factory=dict)


class AccountManager:
    def __init__(self, adb_controller, max_concurrent_threads: int = 10):
        self.adb = adb_controller
        self.accounts: Dict[str, Account] = {}
        self.current_tasks: Dict[str, str] = {}  # Track current task for each account
        self.callbacks: List[Callable] = []
        self.accounts_file = "accounts.json"

        # Thread pool management
        self.max_concurrent_threads = max_concurrent_threads
        self.account_queue = queue.Queue()  # FIFO queue for accounts to process
        self.worker_threads: List[threading.Thread] = []
        self.running_accounts = set()  # Set of account IDs currently being processed
        self.active_accounts = set()  # Set of account IDs that should be running
        self.stop_event = threading.Event()
        self.account_stop_events: Dict[str, threading.Event] = {}  # Individual stop events for each account
        self.task_function = None  # Store the task function

        # Party synchronization
        self.party_sync_points = {}  # party_name -> {sync_point: set(account_ids)}
        self.party_sync_lock = threading.Lock()
        self.party_sync_skipped = set()  # Set of account_ids that have skipped party sync

        self.load_accounts()
        self._start_worker_threads()

    def _start_worker_threads(self):
        """
        Start worker threads that process accounts from queue
        """
        for i in range(self.max_concurrent_threads):
            worker = threading.Thread(
                target=self._worker_loop,
                args=(i,),
                daemon=True
            )
            worker.start()
            self.worker_threads.append(worker)
        logger.info(f"Started {self.max_concurrent_threads} worker threads")

    def _worker_loop(self, worker_id: int):
        """
        Worker thread loop - continuously process accounts from queue
        """
        logger.info(f"Worker {worker_id} started and ready for processing")

        while not self.stop_event.is_set():
            try:
                # Check if this worker should be active based on current max_threads
                if worker_id >= self.max_concurrent_threads:
                    logger.debug(f"Worker {worker_id} sleeping (exceeds max_threads {self.max_concurrent_threads})")
                    time.sleep(1)
                    continue

                # Check if we already have enough running accounts
                if len(self.running_accounts) >= self.max_concurrent_threads:
                    logger.debug(f"Worker {worker_id} waiting (max concurrent reached: {len(self.running_accounts)}/{self.max_concurrent_threads})")
                    time.sleep(1)
                    continue

                # Get next account from queue (blocking with timeout)
                try:
                    account_id = self.account_queue.get(timeout=1)
                    logger.debug(f"Worker {worker_id} picked up account {account_id} from queue")
                except queue.Empty:
                    continue

                # Check if account should still be running
                if account_id not in self.active_accounts or account_id not in self.accounts:
                    logger.debug(f"Worker {worker_id} skipping account {account_id} (not active or not found)")
                    self.account_queue.task_done()
                    continue

                account = self.accounts[account_id]
                logger.info(f"Worker {worker_id} starting to process account {account.name} ({account_id})")

                # Mark as running and connect device
                self.running_accounts.add(account_id)
                account.status = AccountStatus.RUNNING
                # Don't notify callbacks from worker thread - will be handled by GUI polling

                logger.info(f"Worker {worker_id} processing account {account.name} (running: {len(self.running_accounts)}/{self.max_concurrent_threads})")

                # Create or get account-specific stop event
                if account_id not in self.account_stop_events:
                    self.account_stop_events[account_id] = threading.Event()
                account_stop_event = self.account_stop_events[account_id]

                # Connect device only when starting to process
                device_connected = False
                try:
                    logger.info(f"Worker {worker_id} connecting to device {account.device_id} for account {account.name}")
                    self.adb.set_device_active(account.device_id, True)
                    device_connected = True
                    logger.debug(f"Worker {worker_id} device connection successful for account {account.name}")

                    # Run the task function once with account-specific stop event
                    if self.task_function:
                        logger.debug(f"Worker {worker_id} executing task function for account {account.name}")
                        self.task_function(account, self.adb, self, account_stop_event, threading.Event())
                        logger.debug(f"Worker {worker_id} task function completed for account {account.name}")
                    else:
                        logger.warning(f"Worker {worker_id} no task function defined for account {account.name}")

                except Exception as e:
                    logger.error(f"Worker {worker_id} error processing account {account.name}: {str(e)}")
                    account.status = AccountStatus.ERROR
                    account.error_message = str(e)
                    # Don't notify callbacks from worker thread

                finally:
                    # Disconnect device when done processing
                    if device_connected:
                        logger.info(f"Worker {worker_id} disconnecting from device {account.device_id} for account {account.name}")
                        self.adb.set_device_active(account.device_id, False)
                        logger.debug(f"Worker {worker_id} device disconnection completed for account {account.name}")

                    # Remove from running set
                    self.running_accounts.discard(account_id)
                    logger.debug(f"Worker {worker_id} removed account {account.name} from running set")

                    # If account should still be active, put it back in queue after delay
                    if account_id in self.active_accounts and not self.stop_event.is_set():
                        # Add delay between cycles (configurable)
                        from ..config.config import load_game_config
                        game_config = load_game_config()
                        cycle_delay = game_config.get('loop_delay', 30)

                        logger.info(f"Worker {worker_id} waiting {cycle_delay}s before re-queuing account {account.name}")
                        time.sleep(cycle_delay)

                        # Re-check if still active after delay
                        if account_id in self.active_accounts and not self.stop_event.is_set():
                            self.account_queue.put(account_id)
                            # Set back to PENDING when re-queued
                            account.status = AccountStatus.PENDING
                            logger.info(f"Worker {worker_id} re-queued account {account.name} for next cycle")
                        else:
                            logger.debug(f"Worker {worker_id} account {account.name} no longer active, not re-queuing")
                    else:
                        # Account stopped, clean up
                        logger.info(f"Worker {worker_id} account {account.name} stopped, cleaning up")
                        account.status = AccountStatus.STOPPED
                        # Don't notify callbacks from worker thread

                    self.account_queue.task_done()

            except Exception as e:
                logger.error(f"Worker {worker_id} error: {str(e)}")
                time.sleep(1)

        logger.debug(f"Worker {worker_id} stopped")



    def set_max_concurrent_threads(self, max_threads: int):
        """
        Set maximum number of concurrent threads
        """
        old_max = self.max_concurrent_threads
        self.max_concurrent_threads = max_threads
        logger.info(f"Max concurrent threads changed from {old_max} to {max_threads}")

        # If reducing threads, we need to restart worker pool
        if max_threads < old_max:
            logger.warning("Reducing thread count requires restart to take full effect")

        # If increasing threads, start additional workers
        elif max_threads > old_max:
            current_workers = len(self.worker_threads)
            for i in range(current_workers, max_threads):
                worker = threading.Thread(
                    target=self._worker_loop,
                    args=(i,),
                    daemon=True
                )
                worker.start()
                self.worker_threads.append(worker)
                logger.info(f"Started additional worker {i}")

    def get_queue_status(self) -> dict:
        """
        Get current queue status
        """
        return {
            "running_accounts": len(self.running_accounts),
            "active_accounts": len(self.active_accounts),
            "max_concurrent": self.max_concurrent_threads,
            "queued_accounts": self.account_queue.qsize(),
            "available_slots": max(0, self.max_concurrent_threads - len(self.running_accounts))
        }

    def add_account(self, account: Account) -> bool:
        if account.id in self.accounts:
            logger.error(f"Account {account.id} already exists")
            return False
        
        self.accounts[account.id] = account
        self.save_accounts()
        self._notify_callbacks("account_added", account)
        return True
    
    def remove_account(self, account_id: str) -> bool:
        if account_id not in self.accounts:
            logger.warning(f"Cannot remove account {account_id}: account not found")
            return False

        account = self.accounts[account_id]
        logger.info(f"Removing account {account.name} ({account_id})")

        # Stop account first if it's running
        self.stop_account(account_id)

        # Remove from accounts dict
        del self.accounts[account_id]

        # Clear any remaining task info
        self.current_tasks.pop(account_id, None)

        # Clean up stop event and party sync skip flag
        self.account_stop_events.pop(account_id, None)
        self.party_sync_skipped.discard(account_id)

        # Save to file
        self.save_accounts()

        # Notify callbacks
        self._notify_callbacks("account_removed", account_id)

        logger.info(f"Successfully removed account {account.name} ({account_id})")
        return True
    
    def update_account(self, account: Account) -> bool:
        if account.id not in self.accounts:
            logger.warning(f"Cannot update account {account.id}: account not found")
            return False

        old_account = self.accounts[account.id]
        logger.info(f"Updating account {old_account.name} -> {account.name} ({account.id})")

        # Update account in dict
        self.accounts[account.id] = account

        # Save to file
        self.save_accounts()

        # Notify callbacks
        self._notify_callbacks("account_updated", account)

        logger.info(f"Successfully updated account {account.name} ({account.id})")
        return True
    
    def start_account(self, account_id: str, task_function: Callable) -> bool:
        if account_id not in self.accounts:
            logger.error(f"Cannot start account {account_id}: account not found")
            return False

        account = self.accounts[account_id]
        if account_id in self.active_accounts:
            logger.warning(f"Account {account.name} ({account_id}) is already active")
            return False

        logger.info(f"Starting account {account.name} ({account_id})")

        # Store task function
        self.task_function = task_function

        # Reset account-specific stop event
        if account_id not in self.account_stop_events:
            self.account_stop_events[account_id] = threading.Event()
        else:
            self.account_stop_events[account_id].clear()
        logger.debug(f"Account {account.name} stop event reset")

        # Add to active accounts and queue
        self.active_accounts.add(account_id)
        self.account_queue.put(account_id)

        # Set status to PENDING (waiting in queue)
        account.status = AccountStatus.PENDING

        # Update task description
        queue_position = self.account_queue.qsize()
        running_count = len(self.running_accounts)

        if running_count >= self.max_concurrent_threads:
            self.set_account_task(account_id, f"Trong hàng đợi (vị trí {queue_position})")
            logger.info(f"Account {account.name} added to queue at position {queue_position}")
        else:
            self.set_account_task(account_id, "Chuẩn bị chạy")
            logger.info(f"Account {account.name} ready to start processing")

        logger.info(f"Account {account.name} added to processing queue (running: {running_count}/{self.max_concurrent_threads}, queued: {queue_position})")
        return True
    
    def stop_account(self, account_id: str) -> bool:
        if account_id not in self.accounts:
            logger.error(f"Cannot stop account {account_id}: account not found")
            return False

        account = self.accounts[account_id]
        logger.info(f"Stopping account {account.name} ({account_id})")

        # Remove from active accounts (this will stop it from being re-queued)
        self.active_accounts.discard(account_id)
        logger.debug(f"Account {account.name} removed from active accounts")

        # Set account-specific stop event to signal immediate stop
        if account_id in self.account_stop_events:
            self.account_stop_events[account_id].set()
            logger.debug(f"Account {account.name} stop event set")

        # Clear current task
        self.clear_account_task(account_id)

        account.status = AccountStatus.STOPPED
        logger.info(f"Account {account.name} status set to STOPPED")

        # Log queue status after stopping
        queue_status = self.get_queue_status()
        logger.info(f"Account {account.name} stopped. Queue status: {queue_status['running_accounts']}/{queue_status['max_concurrent']} running, {queue_status['queued_accounts']} queued")

        return True

    def start_all_accounts(self, task_function: Callable) -> int:
        """
        Start all accounts (will be processed by thread pool)
        """
        started_count = 0
        for account_id in self.accounts.keys():
            if account_id not in self.active_accounts:
                if self.start_account(account_id, task_function):
                    started_count += 1

        queue_status = self.get_queue_status()
        logger.info(f"Start all requested. {started_count} accounts added to processing. Status: {queue_status['running_accounts']}/{queue_status['max_concurrent']} running, {queue_status['queued_accounts']} queued")
        return started_count

    def stop_all_accounts(self) -> int:
        """
        Stop all accounts and clear queue
        """
        # Get count before stopping
        active_count = len(self.active_accounts)

        # Clear active accounts (this stops re-queuing)
        self.active_accounts.clear()

        # Clear the queue
        queue_size = self.account_queue.qsize()
        while not self.account_queue.empty():
            try:
                account_id = self.account_queue.get_nowait()
                self.account_queue.task_done()
            except queue.Empty:
                break

        # Update all account statuses
        for account in self.accounts.values():
            if account.status in [AccountStatus.RUNNING, AccountStatus.PENDING]:
                account.status = AccountStatus.STOPPED
            self.clear_account_task(account.id)

        logger.info(f"Stop all completed. {active_count} accounts stopped, {queue_size} items removed from queue")
        return active_count

    def pause_account(self, account_id: str) -> bool:
        # For thread pool model, pause means temporarily remove from active
        if account_id not in self.accounts or account_id not in self.active_accounts:
            return False

        account = self.accounts[account_id]
        account.status = AccountStatus.PAUSED
        self.set_account_task(account_id, "Tạm dừng")
        logger.info(f"Account {account_id} paused")
        return True

    def resume_account(self, account_id: str) -> bool:
        if account_id not in self.accounts:
            return False

        account = self.accounts[account_id]
        if account.status != AccountStatus.PAUSED:
            return False

        # Re-add to queue if still active
        if account_id in self.active_accounts:
            self.account_queue.put(account_id)
            account.status = AccountStatus.PENDING
            self.set_account_task(account_id, "Tiếp tục")
            logger.info(f"Account {account_id} resumed")
            return True
        return False
    
    def set_account_task(self, account_id: str, task_description: str):
        """
        Set current task description for an account with logging
        """
        if account_id in self.accounts:
            account_name = self.accounts[account_id].name
            logger.debug(f"Account {account_name} ({account_id}) task: {task_description}")
        else:
            logger.debug(f"Account {account_id} task: {task_description}")

        self.current_tasks[account_id] = task_description
        # Notify callbacks about task change
        self._notify_callbacks("task_updated", {"account_id": account_id, "task": task_description})

    def get_account_task_info(self, account_id: str) -> Optional[str]:
        """
        Get current task description for an account
        """
        return self.current_tasks.get(account_id, None)

    def clear_account_task(self, account_id: str):
        """
        Clear current task for an account
        """
        if account_id in self.current_tasks:
            del self.current_tasks[account_id]

    def register_callback(self, callback: Callable):
        self.callbacks.append(callback)

    def _notify_callbacks(self, event: str, data):
        """
        Notify callbacks - should only be called from main thread
        """
        for callback in self.callbacks:
            try:
                callback(event, data)
            except Exception as e:
                logger.error(f"Callback error: {str(e)}")
    
    def save_accounts(self):
        data = {}
        for acc_id, account in self.accounts.items():
            data[acc_id] = {
                "id": account.id,
                "name": account.name,
                "device_id": account.device_id,
                "username": account.username,
                "password": account.password,
                "party": account.party,
                "loop": account.loop,
                # "server": account.server,
                # "character": account.character,
                "settings": account.settings
            }

        with open(self.accounts_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    
    def load_accounts(self):
        if not os.path.exists(self.accounts_file):
            logger.info("No accounts file found")
            return

        try:
            with open(self.accounts_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            for acc_id, acc_data in data.items():
                account = Account(
                    id=acc_data["id"],
                    name=acc_data["name"],
                    device_id=acc_data["device_id"],
                    username=acc_data["username"],
                    password=acc_data["password"],
                    party=acc_data.get("party", ""),
                    loop=acc_data.get("loop", False),
                    # server=acc_data.get("server", ""),
                    # character=acc_data.get("character", ""),
                    settings=acc_data.get("settings", {})
                )
                self.accounts[acc_id] = account
                logger.info(f"Loaded account: {account.name} ({account.id})")

            logger.info(f"Total {len(self.accounts)} accounts loaded")
        except Exception as e:
            logger.error(f"Error loading accounts: {str(e)}")

    # Party synchronization methods
    def wait_for_party_at_sync_point(self, account_id: str, sync_point: str, timeout: int = 300) -> str:
        """
        Wait for all party members to reach a synchronization point

        Args:
            account_id: ID of the account
            sync_point: Name of the sync point (e.g., "xa_phu")
            timeout: Maximum time to wait in seconds

        Returns:
            String indicating the result:
            - "skipped_no_party": Account has no party
            - "skipped_other_party_waiting": Other party is waiting, skipped
            - "success": All party members reached sync point
            - "timeout": Timeout waiting for party members
        """
        import time

        account = self.accounts.get(account_id)
        if not account or not account.party:
            # No party, proceed immediately
            logger.info(f"Account {account_id} has no party, proceeding immediately")
            return "skipped_no_party"

        party_name = account.party
        logger.info(f"Account {account.name} ({account_id}) waiting for party '{party_name}' at sync point '{sync_point}'")

        # Add a sync completion flag for this party/sync_point
        sync_key = f"{party_name}_{sync_point}"
        if not hasattr(self, 'party_sync_completed'):
            self.party_sync_completed = set()

        with self.party_sync_lock:
            # Check if this account has been skipped
            if account_id in self.party_sync_skipped:
                logger.info(f"Account {account_id} party sync was skipped - proceeding immediately")
                self.party_sync_skipped.discard(account_id)  # Clean up
                return "skipped_other_party_waiting"

            # Check if sync already completed
            if sync_key in self.party_sync_completed:
                logger.info(f"Sync point '{sync_point}' for party '{party_name}' already completed - account {account_id} proceeding")
                return "success"

            # Check if any other party is currently waiting at this sync point
            other_parties_waiting = []
            for other_party, sync_points in self.party_sync_points.items():
                if other_party != party_name and sync_point in sync_points and len(sync_points[sync_point]) > 0:
                    other_parties_waiting.append(other_party)

            if other_parties_waiting:
                logger.info(f"Party '{party_name}' skipping sync at '{sync_point}' because other parties {other_parties_waiting} are waiting - account {account_id} proceeding immediately")
                return "skipped_other_party_waiting"

            # Initialize party sync point if not exists
            if party_name not in self.party_sync_points:
                self.party_sync_points[party_name] = {}
            if sync_point not in self.party_sync_points[party_name]:
                self.party_sync_points[party_name][sync_point] = set()

            # Add this account to sync point
            self.party_sync_points[party_name][sync_point].add(account_id)

            # Get all party members that are active (running or pending)
            party_members = self.get_active_party_members(party_name)
            arrived_members = self.party_sync_points[party_name][sync_point]

            logger.info(f"Party '{party_name}' sync point '{sync_point}': {len(arrived_members)}/{len(party_members)} members arrived")
            logger.info(f"Party members: {party_members}")
            logger.info(f"Arrived members: {arrived_members}")

            # Check if all party members have arrived (compare set sizes and membership)
            if len(arrived_members) >= len(party_members) and arrived_members.issuperset(party_members):
                logger.info(f"All party members arrived at sync point '{sync_point}' for party '{party_name}' - marking as completed")
                # Mark sync as completed so other waiting threads can proceed
                self.party_sync_completed.add(sync_key)
                return "success"

        # Wait for other party members or sync completion
        start_time = time.time()
        while time.time() - start_time < timeout:
            time.sleep(1)  # Check every second

            with self.party_sync_lock:
                # Check if this account has been skipped
                if account_id in self.party_sync_skipped:
                    logger.info(f"Account {account_id} party sync was skipped during wait - proceeding immediately")
                    self.party_sync_skipped.discard(account_id)  # Clean up
                    return "skipped_other_party_waiting"

                # Check if sync was completed by another thread
                if sync_key in self.party_sync_completed:
                    logger.info(f"Sync point '{sync_point}' for party '{party_name}' completed by another member - account {account_id} proceeding")
                    return "success"

                if party_name in self.party_sync_points and sync_point in self.party_sync_points[party_name]:
                    party_members = self.get_active_party_members(party_name)
                    arrived_members = self.party_sync_points[party_name][sync_point]

                    logger.debug(f"Checking sync: {len(arrived_members)}/{len(party_members)} arrived for party '{party_name}'")

                    # Check if all party members have arrived (compare set sizes and membership)
                    if len(arrived_members) >= len(party_members) and arrived_members.issuperset(party_members):
                        logger.info(f"All party members arrived at sync point '{sync_point}' for party '{party_name}' - account {account_id} proceeding")
                        # Mark sync as completed
                        self.party_sync_completed.add(sync_key)
                        return "success"

        logger.warning(f"Timeout waiting for party '{party_name}' at sync point '{sync_point}' - account {account_id}")
        return "timeout"

    def get_active_party_members(self, party_name: str) -> set:
        """
        Get set of account IDs that are in the party and currently active (running or pending)
        """
        party_members = set()
        for acc_id, account in self.accounts.items():
            if (account.party == party_name and
                acc_id in self.active_accounts and
                account.status in [AccountStatus.RUNNING, AccountStatus.PENDING]):
                party_members.add(acc_id)
        return party_members

    def clear_party_sync_point(self, party_name: str, sync_point: str):
        """
        Clear a specific sync point for a party (useful for cleanup)
        """
        with self.party_sync_lock:
            if (party_name in self.party_sync_points and
                sync_point in self.party_sync_points[party_name]):
                self.party_sync_points[party_name][sync_point].clear()
                logger.info(f"Cleared sync point '{sync_point}' for party '{party_name}'")

    def notify_party_sync_complete(self, account_id: str, sync_point: str):
        """
        Notify that an account has completed the sync point and clear if all are done
        """
        account = self.accounts.get(account_id)
        if not account or not account.party:
            return

        party_name = account.party
        sync_key = f"{party_name}_{sync_point}"

        with self.party_sync_lock:
            if (party_name in self.party_sync_points and
                sync_point in self.party_sync_points[party_name]):

                # Remove this account from sync point
                self.party_sync_points[party_name][sync_point].discard(account_id)

                # If no more accounts waiting, clear the sync point and completion flag
                if len(self.party_sync_points[party_name][sync_point]) == 0:
                    logger.info(f"All accounts completed sync point '{sync_point}' for party '{party_name}' - clearing")
                    del self.party_sync_points[party_name][sync_point]

                    # Clear completion flag for next use
                    if hasattr(self, 'party_sync_completed'):
                        self.party_sync_completed.discard(sync_key)

    def skip_party_sync(self, account_id: str) -> bool:
        """
        Skip party synchronization for an account that is currently waiting
        """
        account = self.accounts.get(account_id)
        if not account:
            logger.error(f"Cannot skip party sync: account {account_id} not found")
            return False

        if not account.party:
            logger.info(f"Account {account_id} has no party, nothing to skip")
            return True

        party_name = account.party
        logger.info(f"Skipping party sync for account {account.name} ({account_id}) in party '{party_name}'")

        with self.party_sync_lock:
            # Add account to skip set to signal waiting loop to exit
            self.party_sync_skipped.add(account_id)

            # Remove account from all sync points for this party
            if party_name in self.party_sync_points:
                for sync_point, waiting_accounts in self.party_sync_points[party_name].items():
                    if account_id in waiting_accounts:
                        waiting_accounts.discard(account_id)
                        logger.info(f"Removed account {account_id} from sync point '{sync_point}' for party '{party_name}'")

        # Update task to indicate skip
        self.set_account_task(account_id, "Bỏ qua đợi party - tiếp tục")
        logger.info(f"Account {account.name} ({account_id}) party sync skipped")
        return True