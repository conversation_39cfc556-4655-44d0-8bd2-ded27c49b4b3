import subprocess
import re
import time
from typing import List, Optional, Tuple
import logging
from PIL import Image
import numpy as np
import cv2
import io
from ..config.config import load_config
import os
import threading

logger = logging.getLogger(__name__)


class ADBController:
    def __init__(self):
        config = load_config()
        self.adb_path = config.get("adb_path", r"C:\LDPlayer\LDPlayer9\adb.exe")
        self.ldconsole_path = config.get("ldconsole_path", r"C:\LDPlayer\LDPlayer9\ldconsole.exe")
        self.devices = []
        self.device_names = {}  # Mapping device_id -> display_name
        self.connected_devices = set()  # Track connected devices
        self.active_devices = set()  # Track devices with running accounts
        self.screenshot_lock = threading.Lock()  # Lock để đảm bảo thread-safe
        self._cleanup_temp_files()  # Dọn dẹp file tạm thời cũ

    def _cleanup_temp_files(self):
        """Dọn dẹp các file screenshot tạm thời cũ"""
        temp_dir = "temp_screenshots"
        if os.path.exists(temp_dir):
            try:
                for filename in os.listdir(temp_dir):
                    if filename.startswith("screen_temp_") and filename.endswith(".png"):
                        file_path = os.path.join(temp_dir, filename)
                        os.remove(file_path)
                        logger.debug(f"Cleaned up temp file: {file_path}")
            except Exception as e:
                logger.warning(f"Error cleaning temp files: {str(e)}")

    def execute_command(self, command: str, device_id: Optional[str] = None) -> str:
        cmd = [self.adb_path]
        if device_id:
            cmd.extend(["-s", device_id])
        cmd.extend(command.split())
        
        try:
            # logger.info(f"Executing ADB command: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                logger.error(f"ADB command failed: {result.stderr}")
                return ""
            return result.stdout
        except subprocess.TimeoutExpired:
            logger.error(f"ADB command timeout: {' '.join(cmd)}")
            return ""
        except Exception as e:
            logger.error(f"ADB command error: {str(e)}")
            return ""

    def get_ldplayer_instances(self) -> dict:
        """
        Get LDPlayer instances with their names and ports
        Returns: dict mapping device_id -> display_name
        """
        instances = {}
        try:
            # Get list of LDPlayer instances
            cmd = [self.ldconsole_path, "list2"]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    if line.strip():
                        # Format: index,name,top_window_handle,bind_window_handle,is_running,pid,vbox_pid
                        parts = line.split(',')
                        if len(parts) >= 0:
                            if parts[0].strip() == "99999":
                                continue
                        if len(parts) >= 5:
                            index = parts[0].strip()
                            name = parts[1].strip()
                            is_running = parts[4].strip()
                            if is_running == "1":  # Only running instances
                                # Calculate port: LDPlayer uses index * 2 + 5555
                                port = int(index) * 2 + 5555
                                device_id = f"127.0.0.1:{port}"
                                instances[device_id] = name
                                logger.debug(f"Found LDPlayer instance: {name} -> {device_id}")

        except Exception as e:
            logger.warning(f"Could not get LDPlayer instances: {str(e)}")

        return instances

    def refresh_devices(self) -> List[str]:
        """
        Check connection on each port to get list of devices
        """
        devices = []

        # First, try to get LDPlayer instances for better mapping
        ldplayer_instances = self.get_ldplayer_instances()
        # If we have LDPlayer instances, prioritize those
        if ldplayer_instances:
            logger.info("Using LDPlayer instance detection")
            for device_id, name in ldplayer_instances.items():
                try:
                    # Try to connect to the device
                    cmd = [self.adb_path, "connect", device_id]
                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        # Verify the device is actually responsive
                        test_cmd = [self.adb_path, "-s", device_id, "shell", "echo", "test"]
                        test_result = subprocess.run(test_cmd, capture_output=True, text=True, timeout=3)

                        if test_result.returncode == 0:
                            devices.append(device_id)
                            self.device_names[device_id] = name
                            self.connected_devices.add(device_id)
                            logger.info(f"Found LDPlayer: {name} -> {device_id}")
                        # Disconnect if not responsive
                        subprocess.run([self.adb_path, "disconnect", device_id],
                                        capture_output=True, text=True, timeout=3)
                    else:
                        if result.returncode == 1 and "daemon not running" in result.stdout.lower():
                            subprocess.run([self.adb_path, "kill-server"], capture_output=True, text=True, timeout=3)
                            subprocess.run([self.adb_path, "start-server"], capture_output=True, text=True, timeout=3)

                except (subprocess.TimeoutExpired, Exception) as e:
                    logger.debug(f"LDPlayer {name} ({device_id}) not available: {str(e)}")
                    continue
        else:
            # Fallback to port scanning
            logger.info("Fallback to port scanning")
            # LDPlayer uses odd ports starting from 5555: 5555, 5557, 5559, etc.
            # Scan up to 20 possible ports (should cover most use cases)
            port_range = range(5555, 5595, 2)  # 5555 to 5593

            for port in port_range:
                device_id = f"127.0.0.1:{port}"
                try:
                    # Try to connect to the port
                    cmd = [self.adb_path, "connect", device_id]
                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=5)

                    if result.returncode == 0 and "connected" in result.stdout.lower():
                        # Verify the device is actually responsive
                        test_cmd = [self.adb_path, "-s", device_id, "shell", "echo", "test"]
                        test_result = subprocess.run(test_cmd, capture_output=True, text=True, timeout=3)

                        if test_result.returncode == 0:
                            devices.append(device_id)
                            # Try to guess name from port
                            index = (port - 5555) // 2
                            self.device_names[device_id] = f"LDPlayer-{index}" if index > 0 else "LDPlayer"
                            self.connected_devices.add(device_id)
                            logger.info(f"Found device: {device_id}")
                        # Disconnect if not responsive
                        subprocess.run([self.adb_path, "disconnect", device_id],
                                        capture_output=True, text=True, timeout=3)

                except (subprocess.TimeoutExpired, Exception) as e:
                    logger.debug(f"Port {port} not available: {str(e)}")
                    continue

        self.devices = devices

        # Disconnect devices that are not active (no running accounts)
        self._cleanup_inactive_connections()

        logger.info(f"Total active devices found: {len(devices)}")
        return devices

    def _cleanup_inactive_connections(self):
        """
        Disconnect devices that don't have running accounts
        """
        for device_id in self.connected_devices.copy():
            if device_id not in self.active_devices:
                try:
                    subprocess.run([self.adb_path, "disconnect", device_id],
                                 capture_output=True, text=True, timeout=3)
                    self.connected_devices.discard(device_id)
                    logger.info(f"Disconnected inactive device: {device_id}")
                except Exception as e:
                    logger.debug(f"Error disconnecting {device_id}: {str(e)}")

    def connect_device(self, device_id: str) -> bool:
        """
        Connect to a specific device with improved logging
        """
        logger.debug(f"Attempting to connect to device: {device_id}")

        try:
            cmd = [self.adb_path, "connect", device_id]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=5)

            if result.returncode == 0:
                logger.debug(f"ADB connect command successful for device: {device_id}")

                # Verify the device is responsive
                test_cmd = [self.adb_path, "-s", device_id, "shell", "echo", "test"]
                test_result = subprocess.run(test_cmd, capture_output=True, text=True, timeout=3)

                if test_result.returncode == 0:
                    self.connected_devices.add(device_id)
                    logger.info(f"Successfully connected and verified device: {device_id}")
                    return True
                else:
                    logger.warning(f"Device {device_id} connected but not responsive")
            else:
                logger.warning(f"ADB connect failed for device {device_id}: {result.stderr}")

            return False
        except Exception as e:
            logger.error(f"Exception while connecting to device {device_id}: {str(e)}")
            return False

    def disconnect_device(self, device_id: str) -> bool:
        """
        Disconnect from a specific device with improved logging
        """
        logger.debug(f"Attempting to disconnect from device: {device_id}")

        try:
            result = subprocess.run([self.adb_path, "disconnect", device_id],
                                  capture_output=True, text=True, timeout=3)
            self.connected_devices.discard(device_id)

            if result.returncode == 0:
                logger.info(f"Successfully disconnected from device: {device_id}")
            else:
                logger.warning(f"ADB disconnect command failed for device {device_id}: {result.stderr}")

            return True
        except Exception as e:
            logger.error(f"Exception while disconnecting from device {device_id}: {str(e)}")
            return False

    def set_device_active(self, device_id: str, active: bool = True):
        """
        Mark device as active (has running account) or inactive with improved logging
        """
        if active:
            logger.debug(f"Setting device {device_id} as active")
            self.active_devices.add(device_id)

            # Ensure device is connected when marked as active
            if device_id not in self.connected_devices:
                logger.info(f"Device {device_id} not connected, attempting connection")
                success = self.connect_device(device_id)
                if not success:
                    logger.error(f"Failed to connect device {device_id} when setting active")
            else:
                logger.debug(f"Device {device_id} already connected")
        else:
            logger.debug(f"Setting device {device_id} as inactive")
            self.active_devices.discard(device_id)

            # Disconnect immediately when marked as inactive
            if device_id in self.connected_devices:
                logger.info(f"Disconnecting inactive device {device_id}")
                self.disconnect_device(device_id)
            else:
                logger.debug(f"Device {device_id} already disconnected")

    def get_device_display_name(self, device_id: str) -> str:
        """
        Get display name for device (LDPlayer name if available, otherwise device_id)
        """
        return self.device_names.get(device_id, device_id)

    def get_devices_with_names(self) -> List[Tuple[str, str]]:
        """
        Get list of devices with their display names
        Returns: List of (device_id, display_name) tuples
        """
        return [(device_id, self.get_device_display_name(device_id)) for device_id in self.devices]

    def tap(self, device_id: str, x: int, y: int) -> bool:
        output = self.execute_command(f"shell input tap {x} {y}", device_id)
        return output is not None


    
    def swipe(self, device_id: str, x1: int, y1: int, x2: int, y2: int, duration: int = 300) -> bool:
        output = self.execute_command(f"shell input swipe {x1} {y1} {x2} {y2} {duration}", device_id)
        return output is not None


    
    def input_text(self, device_id: str, text: str, clear_before: bool = True) -> bool:
        if clear_before:
            for _ in range(15):  # Xoá tối đa 50 ký tự
                self.execute_command("shell input keyevent KEYCODE_DEL", device_id)

        text = text.replace(' ', '%s')
        output = self.execute_command(f"shell input text {text}", device_id)
        self.execute_command("shell input keyevent KEYCODE_ENTER", device_id)
        return output is not None
    
    def screenshot(self, device_id: str, text: str = None) -> Optional[np.ndarray]:
        with self.screenshot_lock:
            try:
                result = subprocess.run(
                    [self.adb_path, "-s", device_id, "shell", "screencap", "-p"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    timeout=5
                )

                if result.returncode != 0:
                    logger.error(f"ADB screencap failed: {result.stderr.decode(errors='ignore')}")
                    return None

                if not result.stdout:
                    logger.error(f"No screenshot data for device {device_id}")
                    return None

                # Fix CRLF → LF
                fixed_data = result.stdout.replace(b'\r\n', b'\n')

                img_array = np.frombuffer(fixed_data, np.uint8)
                img = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
                if img is None:
                    logger.error(f"Failed to decode screenshot for device {device_id}, raw size: {len(fixed_data)} bytes")
                    return None
                return img
            except Exception as e:
                logger.error(f"Screenshot error for device {device_id}: {str(e)}")
            return None
    
    def get_screen_size(self, device_id: str) -> Tuple[int, int]:
        output = self.execute_command("shell wm size", device_id)
        match = re.search(r'(\d+)x(\d+)', output)
        if match:
            return int(match.group(1)), int(match.group(2))
        return 1920, 1080
    
    def start_app(self, device_id: str, package_name: str, activity_name: str) -> bool:
        """
        Start an app on the device with improved logging
        """
        logger.info(f"Starting app {package_name} on device {device_id}")

        output = self.execute_command(f"shell am start -n {package_name}/{activity_name}", device_id)
        success = output is not None

        if success:
            logger.info(f"Successfully started app {package_name} on device {device_id}")
        else:
            logger.error(f"Failed to start app {package_name} on device {device_id}")

        return success
    
    def stop_app(self, device_id: str, package_name: str) -> bool:
        output = self.execute_command(f"shell am force-stop {package_name}", device_id)
        return output is not None
    
    def is_app_running(self, device_id: str, package_name: str) -> bool:
        """
        Check if an app is running on the device with improved logging
        """
        logger.debug(f"Checking if app {package_name} is running on device {device_id}")

        output = self.execute_command(f"shell pidof {package_name}", device_id)
        is_running = bool(output and output.strip())

        if is_running:
            logger.debug(f"App {package_name} is running on device {device_id}")
        else:
            logger.debug(f"App {package_name} is not running on device {device_id}")

        return is_running