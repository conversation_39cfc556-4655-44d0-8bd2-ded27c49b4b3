from PyQt5.QtWidgets import (Q<PERSON>ialog, QVBoxLayout, QHBoxLayout, QTabWidget,
                             QWidget, QFormLayout, QSpinBox, QCheckBox,
                             QLineEdit, QPushButton, QDialogButtonBox,
                             QGroupBox, QComboBox, QLabel, QTextEdit)
from PyQt5.QtCore import Qt
from src.config.config import (load_game_config, load_default_account_config,
                               save_game_config, save_default_account_config)

class SettingsDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.game_config = load_game_config()
        self.account_config = load_default_account_config()
        self.init_ui()
        
    def init_ui(self):
        self.setWindowTitle("Cài đặt")
        self.setModal(True)
        self.resize(600, 500)
        
        layout = QVBoxLayout()
        
        self.tabs = QTabWidget()

        # Game settings (chung cho tất cả)
        game_tab = self.create_game_settings_tab()
        self.tabs.addTab(game_tab, "Cài đặt Game")

        # Default account settings (mặc định cho account mới)
        account_tab = self.create_account_settings_tab()
        self.tabs.addTab(account_tab, "Mặc định Account")

        layout.addWidget(self.tabs)
        
        # Buttons
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel,
            Qt.Horizontal, self
        )
        button_box.accepted.connect(self.save_settings)
        button_box.rejected.connect(self.reject)
        
        layout.addWidget(button_box)
        self.setLayout(layout)

    def create_game_settings_tab(self):
        """Tạo tab cài đặt game (chung cho tất cả account)"""
        widget = QWidget()
        layout = QVBoxLayout()

        # ADB settings
        adb_group = QGroupBox("Cài đặt ADB")
        adb_layout = QFormLayout()

        self.game_adb_path_edit = QLineEdit(self.game_config.get("adb_path"))
        adb_layout.addRow("Đường dẫn ADB:", self.game_adb_path_edit)

        self.game_ldconsole_path_edit = QLineEdit(self.game_config.get("ldconsole_path"))
        adb_layout.addRow("Đường dẫn LDConsole:", self.game_ldconsole_path_edit)

        adb_group.setLayout(adb_layout)
        layout.addWidget(adb_group)

        # Game info
        game_group = QGroupBox("Thông tin game")
        game_layout = QFormLayout()

        self.game_package_name_edit = QLineEdit(self.game_config.get("package_name"))
        game_layout.addRow("Package name:", self.game_package_name_edit)

        self.game_activity_name_edit = QLineEdit(self.game_config.get("activity_name"))
        game_layout.addRow("Activity name:", self.game_activity_name_edit)

        self.game_loop_delay = QSpinBox()
        self.game_loop_delay.setRange(1, 60)
        self.game_loop_delay.setSuffix(" giây")
        self.game_loop_delay.setValue(self.game_config.get("loop_delay"))
        game_layout.addRow("Độ trễ vòng lặp:", self.game_loop_delay)

        game_group.setLayout(game_layout)
        layout.addWidget(game_group)

        # Logging settings
        log_group = QGroupBox("Cài đặt log")
        log_layout = QFormLayout()

        self.game_log_level_combo = QComboBox()
        self.game_log_level_combo.addItems(["DEBUG", "INFO", "WARNING", "ERROR"])
        self.game_log_level_combo.setCurrentText(self.game_config.get("log_level"))
        log_layout.addRow("Mức độ log:", self.game_log_level_combo)

        self.game_save_log_check = QCheckBox()
        self.game_save_log_check.setChecked(self.game_config.get("save_log"))
        log_layout.addRow("Lưu log ra file:", self.game_save_log_check)

        log_group.setLayout(log_layout)
        layout.addWidget(log_group)

        layout.addStretch()
        widget.setLayout(layout)
        return widget

    def create_account_settings_tab(self):
        """Tạo tab cài đặt mặc định cho account mới"""
        widget = QWidget()
        layout = QVBoxLayout()

        # Auto settings
        auto_group = QGroupBox("Cài đặt tự động mặc định")
        auto_layout = QFormLayout()

        self.account_auto_login_check = QCheckBox()
        self.account_auto_login_check.setChecked(self.account_config.get("auto_login"))
        auto_layout.addRow("Auto login:", self.account_auto_login_check)

        self.account_login_retry = QSpinBox()
        self.account_login_retry.setRange(1, 10)
        self.account_login_retry.setValue(self.account_config.get("login_retry"))
        auto_layout.addRow("Số lần thử login:", self.account_login_retry)

        self.account_auto_train_check = QCheckBox()
        self.account_auto_train_check.setChecked(self.account_config.get("auto_train"))
        auto_layout.addRow("Auto train:", self.account_auto_train_check)

        self.account_loop_check = QCheckBox()
        self.account_loop_check.setChecked(self.account_config.get("loop", False))
        auto_layout.addRow("Chu kỳ liên tục:", self.account_loop_check)

        auto_group.setLayout(auto_layout)
        layout.addWidget(auto_group)

        # Train settings
        train_group = QGroupBox("Cài đặt train mặc định")
        train_layout = QFormLayout()

        self.account_train_area_combo = QComboBox()
        train_areas = self.game_config.get('train_areas', {})
        for area_id, area_data in train_areas.items():
            self.account_train_area_combo.addItem(area_data['name'], area_id)

        # Set current selection
        current_area = self.account_config.get('train_area')
        for i in range(self.account_train_area_combo.count()):
            if self.account_train_area_combo.itemData(i) == current_area:
                self.account_train_area_combo.setCurrentIndex(i)
                break

        train_layout.addRow("Khu vực:", self.account_train_area_combo)

        self.account_train_map_combo = QComboBox()
        self.account_train_area_combo.currentTextChanged.connect(self.update_account_train_maps)
        self.update_account_train_maps()

        train_layout.addRow("Map:", self.account_train_map_combo)

        self.account_train_x = QSpinBox()
        self.account_train_x.setRange(0, 9999)
        self.account_train_x.setValue(self.account_config.get('train_x'))
        train_layout.addRow("Tọa độ X:", self.account_train_x)

        self.account_train_y = QSpinBox()
        self.account_train_y.setRange(0, 9999)
        self.account_train_y.setValue(self.account_config.get('train_y'))
        train_layout.addRow("Tọa độ Y:", self.account_train_y)

        train_group.setLayout(train_layout)
        layout.addWidget(train_group)

        layout.addStretch()
        widget.setLayout(layout)
        return widget

    def update_account_train_maps(self):
        """Update account train maps based on selected area"""
        self.account_train_map_combo.clear()

        train_areas = self.game_config.get('train_areas', {})
        current_area = self.account_train_area_combo.currentData()
        if current_area and current_area in train_areas:
            maps = train_areas[current_area].get('maps', {})
            for map_id, map_data in maps.items():
                self.account_train_map_combo.addItem(map_data['name'], map_id)

        # Set current selection
        current_map = self.account_config.get('train_map')
        for i in range(self.account_train_map_combo.count()):
            if self.account_train_map_combo.itemData(i) == current_map:
                self.account_train_map_combo.setCurrentIndex(i)
                break








    
    def save_settings(self):
        # Save game settings
        game_settings = {
            "adb_path": self.game_adb_path_edit.text(),
            "ldconsole_path": self.game_ldconsole_path_edit.text(),
            "package_name": self.game_package_name_edit.text(),
            "activity_name": self.game_activity_name_edit.text(),
            "loop_delay": self.game_loop_delay.value(),
            "log_level": self.game_log_level_combo.currentText(),
            "save_log": self.game_save_log_check.isChecked(),
        }
        save_game_config(game_settings)

        # Save default account settings
        account_settings = {
            "auto_login": self.account_auto_login_check.isChecked(),
            "login_retry": self.account_login_retry.value(),
            "auto_train": self.account_auto_train_check.isChecked(),
            "loop": self.account_loop_check.isChecked(),
            "train_area": self.account_train_area_combo.currentData(),
            "train_map": self.account_train_map_combo.currentData(),
            "train_x": self.account_train_x.value(),
            "train_y": self.account_train_y.value(),
        }
        save_default_account_config(account_settings)

        self.accept()