import time
import logging
import threading
from ..core.account_manager import Account
from ..core.adb_controller import ADBController
from ..utils.image_utils import find_image, wait_for_image
from .auto_train import AutoTrainManager
from ..config.config import load_config


logger = logging.getLogger(__name__)


def auto_task(account: Account, adb: ADBController, account_manager,
              stop_event: threading.Event, pause_event: threading.Event):
    """
    Main auto task function for each account
    """

    logger.info(f"Starting auto task cycle for account {account.name} ({account.id})")

    if account.loop:
        account_manager.set_account_task(account.id, "Bắt đầu chu kỳ")

        try:
            # Continuous loop execution when account.loop is True
            while True:
                # Check if stop event is set
                if stop_event.is_set():
                    logger.info(f"Stop event set for account {account.name}, exiting loop")
                    account_manager.set_account_task(account.id, "Dừng")
                    break

                # Check if paused
                if pause_event.is_set():
                    logger.info(f"Account {account.name} is paused, waiting...")
                    account_manager.set_account_task(account.id, "Tạm dừng")
                    time.sleep(5)  # Wait 5 seconds before checking again
                    continue

                # Execute one cycle
                execute_single_cycle(account, adb, account_manager, stop_event, pause_event)

                # Wait before next cycle
                time.sleep(5)

        except Exception as e:
            logger.error(f"Error in auto task loop for {account.name}: {str(e)}")
            raise
    else:
        account_manager.set_account_task(account.id, "Bắt đầu chu kỳ")

        try:
            # Single cycle execution (no infinite loop)
            # Device connection is handled by worker thread
            # This allows the thread pool to process other accounts
            status = execute_single_cycle(account, adb, account_manager, stop_event, pause_event)
            return status
        except Exception as e:
            logger.error(f"Error in auto task for {account.name}: {str(e)}")
            raise


def execute_single_cycle(account: Account, adb: ADBController, account_manager,
                        stop_event: threading.Event, pause_event: threading.Event):
    """
    Execute a single cycle of auto tasks
    """
    config = load_config()  # Load config for this cycle

    logger.debug(f"Executing single cycle for account {account.name}")

    # Check if stop event is set
    if stop_event.is_set():
        logger.info(f"Stop event set for account {account.name}, exiting cycle")
        account_manager.set_account_task(account.id, "Dừng")
        return

    # Check if paused
    if pause_event.is_set():
        logger.info(f"Account {account.name} is paused, skipping cycle")
        account_manager.set_account_task(account.id, "Tạm dừng")
        return

    # Step 1: Check if game is running
    logger.debug(f"Checking if game is running for account {account.name}")
    account_manager.set_account_task(account.id, "Kiểm tra game")
    status = ""
    if not adb.is_app_running(account.device_id, config['package_name']):
        logger.info(f"Game not running for account {account.name}, starting app")
        account_manager.set_account_task(account.id, "Khởi động game")

        success = adb.start_app(account.device_id, config['package_name'], config['activity_name'])
        if not success:
            logger.error(f"Failed to start app for account {account.name}")
            account_manager.set_account_task(account.id, "Lỗi khởi động game")
            return

        logger.debug(f"Waiting for game to load for account {account.name}")
        account_manager.set_account_task(account.id, "Chờ game tải")
        time.sleep(5)

        # Wait for game to load
        if not wait_for_image(adb, account.device_id, "templates/login_button.png", timeout=10):
            logger.error(f"Game failed to load properly for account {account.name}")
            account_manager.set_account_task(account.id, "Lỗi tải game")
            return  # Exit this cycle, will be re-queued

        logger.info(f"Game loaded successfully for account {account.name}")
    else:
        logger.debug(f"Game already running for account {account.name}")

    # Check stop event before continuing
    if stop_event.is_set():
        logger.info(f"Stop event set for account {account.name}, exiting cycle")
        account_manager.set_account_task(account.id, "Dừng")
        return

    # Step 2: Take screenshot and check login status
    logger.debug(f"Taking screenshot for account {account.name}")
    account_manager.set_account_task(account.id, "Chụp màn hình")

    screenshot = adb.screenshot(account.device_id, "123")
    time.sleep(2)
    if screenshot is None:
        logger.error(f"Failed to take screenshot for account {account.name}")
        account_manager.set_account_task(account.id, "Lỗi chụp màn hình")
        return
    # Step 3: Check if on login screen
    logger.debug(f"Checking login screen for account {account.name}")
    login_pos = find_image(screenshot, "templates/login_button.png")
    if login_pos:
        logger.info(f"Login screen detected for account {account.name}")

        # Check auto_login setting from account settings
        if account.settings.get('auto_login', False):
            logger.info(f"Auto login enabled for account {account.name}")
            account_manager.set_account_task(account.id, "Đăng nhập tự động")

            login_success = auto_login(account, adb, config, account_manager)
            if not login_success:
                logger.error(f"Auto login failed for account {account.name}")
                account_manager.set_account_task(account.id, "Lỗi đăng nhập")
                return

            logger.info(f"Auto login successful for account {account.name}")
        else:
            # Auto login is disabled, skip login
            logger.info(f"Auto login disabled for account {account.name}, waiting for manual login")
            account_manager.set_account_task(account.id, "Chờ đăng nhập thủ công")
            return

    # Step 4: Check enter game screen
    logger.debug(f"Checking enter game screen for account {account.name}")
    account_manager.set_account_task(account.id, "Kiểm tra vào game")

    # Take fresh screenshot
    screenshot = adb.screenshot(account.device_id, "157")
    if screenshot is None:
        logger.error(f"Failed to take screenshot for enter game check for account {account.name}")
        account_manager.set_account_task(account.id, "Lỗi chụp màn hình")
        return

    enter_game_pos = find_image(screenshot, "templates/enter_game_button.png")
    if enter_game_pos:
        logger.info(f"Enter game button found for account {account.name}")
        account_manager.set_account_task(account.id, "Vào game")

        adb.tap(account.device_id, enter_game_pos[0], enter_game_pos[1])
        time.sleep(2)

        logger.info(f"Entered game for account {account.name}")

    # Check stop event before performing auto tasks
    if stop_event.is_set():
        logger.info(f"Stop event set for account {account.name}, exiting cycle")
        account_manager.set_account_task(account.id, "Dừng")
        return

    # Step 5: Perform auto tasks based on settings
    logger.debug(f"Checking auto task settings for account {account.name}")

    # Check auto_train setting from account settings
    if account.settings.get('auto_train', False):
        logger.info(f"Auto train enabled for account {account.name}")
        account_manager.set_account_task(account.id, "Kiểm tra auto train")
        status = check_train_tasks(account, adb, account_manager, screenshot)
    else:
        logger.debug(f"Auto train disabled for account {account.name}")

    # Future auto features can be added here
    # if account.settings.get('auto_daily', True):
    #     check_daily_tasks(account, adb, account_manager, screenshot)

    # if account.settings.get('auto_event', True):
    #     check_event_tasks(account, adb, account_manager, screenshot)

    # if account.settings.get('auto_dungeon', False):
    #     check_dungeon_tasks(account, adb, account_manager, screenshot)

    # Step 6: Cycle completed successfully
    logger.info(f"Auto task cycle completed successfully for account {account.name}")
    account_manager.set_account_task(account.id, "Hoàn thành")
    return status


def auto_login(account: Account, adb: ADBController, config: dict, account_manager) -> bool:
    """
    Auto login function with improved logging and task status
    """
    max_retry = account.settings.get('login_retry', config.get('login_retry', 3))

    logger.info(f"Starting auto login for account {account.name} (max {max_retry} attempts)")

    for attempt in range(max_retry):
        logger.info(f"Login attempt {attempt + 1}/{max_retry} for account {account.name}")
        account_manager.set_account_task(account.id, f"Đăng nhập ({attempt + 1}/{max_retry})")

        # Take screenshot
        screenshot = adb.screenshot(account.device_id, "219")
        if screenshot is None:
            logger.error(f"Failed to take screenshot for login attempt {attempt + 1} for account {account.name}")
            account_manager.set_account_task(account.id, f"Lỗi chụp màn hình ({attempt + 1}/{max_retry})")
            time.sleep(2)
            continue

        # Check if already logged in
        if find_image(screenshot, "templates/enter_game_button.png"):
            logger.info(f"Already logged in for account {account.name}")
            account_manager.set_account_task(account.id, "Đã đăng nhập")
            return True

        # Find login button
        login_pos = find_image(screenshot, "templates/login_button.png")
        if login_pos:
            logger.debug(f"Login button found for account {account.name}")
            # Enter username
            account_manager.set_account_task(account.id, f"Nhập tài khoản ({attempt + 1}/{max_retry})")
            username_x = config.get('username_x')
            username_y = config.get('username_y')
            if username_x and username_y:
                logger.debug(f"Entering username for account {account.name}")
                adb.tap(account.device_id, username_x, username_y)
                time.sleep(0.5)
                adb.input_text(account.device_id, account.username)
                time.sleep(1)
            else:
                logger.warning(f"Username coordinates not configured for account {account.name}")

            # Enter password
            account_manager.set_account_task(account.id, f"Nhập mật khẩu ({attempt + 1}/{max_retry})")
            password_x = config.get('password_x')
            password_y = config.get('password_y')
            if password_x and password_y:
                logger.debug(f"Entering password for account {account.name}")
                adb.tap(account.device_id, password_x, password_y)
                time.sleep(0.5)
                adb.input_text(account.device_id, account.password)
                time.sleep(1)
            else:
                logger.warning(f"Password coordinates not configured for account {account.name}")

            # Click login button
            logger.debug(f"Clicking login button for account {account.name}")
            account_manager.set_account_task(account.id, f"Nhấn đăng nhập ({attempt + 1}/{max_retry})")
            adb.tap(account.device_id, login_pos[0], login_pos[1])
            time.sleep(2)

            # Wait for login result
            account_manager.set_account_task(account.id, f"Chờ kết quả đăng nhập ({attempt + 1}/{max_retry})")
            if wait_for_image(adb, account.device_id, "templates/enter_game_button.png", timeout=10):
                logger.info(f"Login successful for account {account.name} on attempt {attempt + 1}")
                account_manager.set_account_task(account.id, "Đăng nhập thành công")
                return True
            else:
                logger.warning(f"Login attempt {attempt + 1} failed for account {account.name}")
        else:
            logger.warning(f"Login button not found for account {account.name} on attempt {attempt + 1}")

        # Wait before retry
        if attempt < max_retry - 1:
            logger.info(f"Waiting before retry for account {account.name}")
            account_manager.set_account_task(account.id, f"Chờ thử lại ({attempt + 2}/{max_retry})")
            time.sleep(3)

    logger.error(f"All login attempts failed for account {account.name}")
    account_manager.set_account_task(account.id, "Đăng nhập thất bại")
    return False


def check_daily_tasks(account: Account, adb: ADBController, account_manager, screenshot):
    """
    Check and complete daily tasks with improved logging
    """
    logger.info(f"Checking daily tasks for account {account.name}")
    account_manager.set_account_task(account.id, "Kiểm tra nhiệm vụ hàng ngày")

    try:
        # Find daily task icon
        daily_pos = find_image(screenshot, "templates/daily_task_icon.png")
        if daily_pos:
            logger.info(f"Daily task icon found for account {account.name}")
            account_manager.set_account_task(account.id, "Mở nhiệm vụ hàng ngày")

            adb.tap(account.device_id, daily_pos[0], daily_pos[1])
            time.sleep(2)

            # Auto claim rewards logic here
            account_manager.set_account_task(account.id, "Nhận thưởng hàng ngày")
            logger.info(f"Processing daily tasks for account {account.name}")
            # TODO: Implement daily task completion logic

        else:
            logger.debug(f"Daily task icon not found for account {account.name}")
            account_manager.set_account_task(account.id, "Không tìm thấy nhiệm vụ hàng ngày")

    except Exception as e:
        logger.error(f"Error in daily tasks for account {account.name}: {str(e)}")
        account_manager.set_account_task(account.id, "Lỗi nhiệm vụ hàng ngày")
        raise


def check_event_tasks(account: Account, adb: ADBController, account_manager, screenshot):
    """
    Check and complete event tasks with improved logging
    """
    logger.info(f"Checking event tasks for account {account.name}")
    account_manager.set_account_task(account.id, "Kiểm tra sự kiện")

    try:
        # Event task logic here
        logger.debug(f"Event task logic not implemented for account {account.name}")
        account_manager.set_account_task(account.id, "Sự kiện chưa được triển khai")
        # TODO: Implement event task logic

    except Exception as e:
        logger.error(f"Error in event tasks for account {account.name}: {str(e)}")
        account_manager.set_account_task(account.id, "Lỗi sự kiện")
        raise


def check_dungeon_tasks(account: Account, adb: ADBController, account_manager, screenshot):
    """
    Check and complete dungeon tasks with improved logging
    """
    logger.info(f"Checking dungeon tasks for account {account.name}")
    account_manager.set_account_task(account.id, "Kiểm tra phó bản")

    try:
        # Dungeon task logic here
        logger.debug(f"Dungeon task logic not implemented for account {account.name}")
        account_manager.set_account_task(account.id, "Phó bản chưa được triển khai")
        # TODO: Implement dungeon task logic

    except Exception as e:
        logger.error(f"Error in dungeon tasks for account {account.name}: {str(e)}")
        account_manager.set_account_task(account.id, "Lỗi phó bản")
        raise


def check_train_tasks(account: Account, adb: ADBController, account_manager, screenshot):
    """
    Check and complete train tasks with improved logging
    """
    logger.info(f"Checking train tasks for account {account.name}")

    try:
        # Initialize AutoTrainManager
        logger.debug(f"Initializing AutoTrainManager for account {account.name}")
        train_manager = AutoTrainManager(adb, account_manager)

        # Check current train status
        logger.debug(f"Checking train status for account {account.name}")
        account_manager.set_account_task(account.id, "Kiểm tra trạng thái train")
        train_status = train_manager.check_train_status(account.device_id)
        logger.debug(f"Train status for account {account.name}: {train_status}")

        # If not training, start auto train
        if not train_status.get('in_train', False):
            logger.info(f"Auto train not active for account {account.name}, starting auto train")
            account_manager.set_account_task(account.id, "Chuẩn bị auto train")

            # Get train settings from account
            train_area = account.settings.get('train_area')
            train_map = account.settings.get('train_map')
            train_x = account.settings.get('train_x')
            train_y = account.settings.get('train_y')

            logger.debug(f"Train settings for account {account.name}: area={train_area}, map={train_map}, pos=({train_x}, {train_y})")

            # Validate train settings
            if not all([train_area, train_map, train_x is not None, train_y is not None]):
                logger.error(f"Incomplete train settings for account {account.name}")
                account_manager.set_account_task(account.id, "Lỗi cấu hình train")
                return

            # Start auto train
            logger.info(f"Starting auto train for account {account.name}")
            account_manager.set_account_task(account.id, "Bắt đầu auto train")
            success = train_manager.start_auto_train(
                account, train_area, train_map, train_x, train_y
            )

            if success:
                logger.info(f"Auto train started successfully for account {account.name}")
                account_manager.set_account_task(account.id, "Đang train")
                return "done"
            else:
                logger.error(f"Failed to start auto train for account {account.name}")
                account_manager.set_account_task(account.id, "Lỗi auto train")
        else:
            logger.debug(f"Account {account.name} is already training")
            account_manager.set_account_task(account.id, "Đang train")
            train_manager.click_skill(account.device_id)
            return "done"
            # Future: Add inventory management
            # train_manager.check_tui_do(account.device_id)
        # Future: Check various conditions that need handling
        # if train_status.get('hp_low', False):
        #     logger.warning(f"HP low for account {account.name}, may need healing")
        #     account_manager.set_account_task(account.id, "HP thấp - cần hồi máu")
        #     # Add healing logic here

        # if train_status.get('mp_low', False):
        #     logger.warning(f"MP low for account {account.name}, may need mana recovery")
        #     account_manager.set_account_task(account.id, "MP thấp - cần hồi mana")
        #     # Add mana recovery logic here

        # if train_status.get('inventory_full', False):
        #     logger.warning(f"Inventory full for account {account.name}, may need to sell items")
        #     account_manager.set_account_task(account.id, "Túi đồ đầy - cần bán đồ")
        #     # Add item selling logic here

    except Exception as e:
        logger.error(f"Error in train tasks for account {account.name}: {str(e)}")
        account_manager.set_account_task(account.id, "Lỗi train tasks")
        raise